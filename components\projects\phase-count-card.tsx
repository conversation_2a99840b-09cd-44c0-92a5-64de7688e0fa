"use client";
import React, { useEffect } from "react";
import { <PERSON>, CardHeader, CardBody } from "@heroui/react";
import { useTheme } from "next-themes";

import { getPhaseStyleText } from "../primitives";

import { Project } from "@/types/projects";

const PhaseCountCard: React.FC<{ projects: Project[]; phase: string }> = ({
  projects,
  phase,
}) => {
  const { theme } = useTheme();
  const [count, setCount] = React.useState(0);

  useEffect(() => {
    if (!projects || !Array.isArray(projects)) {
      console.error("Invalid projects data:", projects);

      return;
    }
    console.log(`Projects: `, projects);

    let c =
      phase === "TOTAL"
        ? projects.filter(
          (p) => p.estado
        ).length
        : projects.filter(
          (p) => p.fase && p.fase.toLocaleLowerCase() === phase.toLowerCase(),
        ).filter(
          (p) => p.estado
        ).length;

    if (c == 0) {
      let d =
        phase === "TOTAL"
          ? projects.filter(
            (p: any) => p.estado
          ).length
          : projects.filter(
            (p: any) =>
              p.actualPhase &&
              p.actualPhase.toLocaleLowerCase() === phase.toLowerCase(),
          ).filter(
            (p: any) => p.estado
          ).length;

      setCount(d);
    } else {
      setCount(c);
    }
  }, [projects, phase]);

  return (
    <Card
      isPressable
      className={`w-28 h-18 rounded-md transition-all hover:scale-105 ${getPhaseStyleText(phase, theme === "dark")}`}
    >
      <CardHeader className="flex justify-center pb-0">
        <h5 className="font-bold text-xs tracking-wide">
          {typeof phase === "string"
            ? phase.toLowerCase() === "incubadora"
              ? "TAKE OFF"
              : phase
            : phase}
        </h5>
      </CardHeader>
      <CardBody className="flex justify-center items-center pt-0">
        <p className="text-xl font-bold">{count}</p>
      </CardBody>
    </Card>
  );
};

export default PhaseCountCard;
