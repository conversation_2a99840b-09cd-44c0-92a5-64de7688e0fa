"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface SubtaskObservation {
  subtask_name: string;
  observation: string;
}

export interface UpdateSubtaskObservationsRequest {
  field_id: number;
  subtask_observations: SubtaskObservation[];
}

export interface SubtaskObservationsResponse {
  phase_id: number;
  phase_name: string;
  fields: {
    field_id: number;
    field_name: string;
    field_type: string;
    subtasks: any[];
    observations_subtasks: { [key: string]: string };
  }[];
}

export function useSubtaskObservations() {
  const [updating, setUpdating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subtaskObservations, setSubtaskObservations] = useState<SubtaskObservationsResponse | null>(null);

  const fetchSubtaskObservations = async (
    projectId: string | number,
    phaseId: number,
  ) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.GET_SUBTASK_OBSERVATIONS
        .replace("{project_id}", String(projectId))
        .replace("{phase_id}", String(phaseId));

      const response = await axiosInstance.get(url);

      if (response.status === 200) {
        setSubtaskObservations(response.data);
        return response.data;
      } else {
        throw new Error("Failed to fetch subtask observations");
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || "Failed to fetch subtask observations";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateSubtaskObservations = async (
    projectId: string | number,
    phaseId: number,
    data: UpdateSubtaskObservationsRequest,
  ) => {
    try {
      setUpdating(true);
      setError(null);

      const url = API_ROUTES.UPDATE_SUBTASK_OBSERVATIONS
        .replace("{project_id}", String(projectId))
        .replace("{phase_id}", String(phaseId));

      const response = await axiosInstance.post(url, data);

      if (response.status === 200) {
        // Refetch observations after successful update
        await fetchSubtaskObservations(projectId, phaseId);
        return response.data;
      } else {
        throw new Error("Failed to update subtask observations");
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || "Failed to update subtask observations";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setUpdating(false);
    }
  };

  return {
    fetchSubtaskObservations,
    updateSubtaskObservations,
    subtaskObservations,
    loading,
    updating,
    error,
  };
}
