import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react"; // Updated import path

import { getPhaseStyle, phaseColors } from "@/components/primitives";
import { ApiProjectResponse } from "@/types/project-api";

//   {
//     label: "START",
//     dates: ["02/01/2025", "04/01/2025"],
//     status: "completed",
//     progress: 100,
//   },
//   {
//     label: "COLLECTION",
//     dates: ["26/01/2025", "20/03/2025"],
//     status: "current",
//     progress: 65,
//   },
//   {
//     label: "MIGRATION",
//     dates: ["15/02/2025", "15/02/2025"],
//     status: "pending",
//     progress: 30,
//   },
//   {
//     label: "TEST",
//     dates: ["16/03/2025", "16/03/2025"],
//     status: "pending",
//     progress: 12,
//   },
//   {
//     label: "GO LIVE",
//     dates: ["27/04/2025", "27/04/2025"],
//     status: "pending",
//     progress: 18,
//   },
//   {
//     label: "INCUBADORA",
//     dates: ["27/05/2025", "27/05/2025"],
//     status: "pending",
//     progress: 7,
//   },
// ];

// Helper function to extract color from phase style
const extractPhaseColors = (phase: string, isDarkMode = false) => {
  isDarkMode = !isDarkMode;
  switch (phase.toLowerCase()) {
    case "incubadora":
      return {
        bg: isDarkMode ? phaseColors.incubadora.bg : "#dbeafe",
        text: isDarkMode ? phaseColors.incubadora.text : "#1d4ed8",
        border: isDarkMode ? phaseColors.incubadora.border : "#1d4ed8",
      };
    case "migration":
      return {
        bg: isDarkMode ? phaseColors.migration.bg : "#fef3c7", // yellow bg
        text: isDarkMode ? phaseColors.migration.text : "#ca8a04", // yellow text
        border: isDarkMode ? phaseColors.migration.border : "#ca8a04", // yellow border
      };
    case "test":
      return {
        bg: isDarkMode ? phaseColors.test.bg : "#f0abfc", // purple bg
        text: isDarkMode ? phaseColors.test.text : "#a855f7", // purple text
        border: isDarkMode ? phaseColors.test.border : "#a855f7", // purple border
      };
    case "go live":
      return {
        bg: isDarkMode ? phaseColors["go live"].bg : "#bbf7d0", // green bg
        text: isDarkMode ? phaseColors["go live"].text : "#4ade80", // green text
        border: isDarkMode ? phaseColors["go live"].border : "#4ade80", // green border
      };
    case "collection":
      return {
        bg: isDarkMode ? phaseColors.collection.bg : "#fef9c3", // yellow bg
        text: isDarkMode ? phaseColors.collection.text : "#ca8a04", // yellow text
        border: isDarkMode ? phaseColors.collection.border : "#ca8a04", // yellow border
      };
    case "start":
      return {
        bg: isDarkMode ? phaseColors.start.bg : "#e0f2fe", // blue bg
        text: isDarkMode ? phaseColors.start.text : "#0284c7", // blue text
        border: isDarkMode ? phaseColors.start.border : "#0284c7", // blue border
      };
    case "default":
    default:
      return {
        bg: isDarkMode ? "rgba(17, 24, 39, 0.5)" : "#e5e7eb", // gray bg
        text: isDarkMode ? "#9ca3af" : "#374151", // gray text
        border: isDarkMode ? "#9ca3af" : "#374151", // gray border
      };
  }
};

export interface ProgressTimelineProps {
  onPhaseSelect?: (phase: string) => void;
  selectedPhase?: string;
  isEditMode?: boolean;
  projectData?: ApiProjectResponse;
}

export const ProgressTimeline: React.FC<ProgressTimelineProps> = ({
  onPhaseSelect,
  selectedPhase,
  isEditMode = false,
  projectData,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [timelineSteps, setTimelineSteps] = useState<any[]>([]);
  const [internalSelectedPhase, setInternalSelectedPhase] = useState<
    string | undefined
  >(selectedPhase);
  const [temporarySelectedPhase, setTemporarySelectedPhase] = useState<
    string | undefined
  >(selectedPhase);

  // Use either the controlled or internal state
  const effectiveSelectedPhase =
    selectedPhase !== undefined ? selectedPhase : internalSelectedPhase;

  const handlePhaseClick = (phase: string) => {
    console.log("Phase clicked:", phase);
    setTemporarySelectedPhase(phase);
    if (isEditMode) {
      setInternalSelectedPhase(phase);
      setIsModalOpen(true);

      return;
    }

    setTemporarySelectedPhase(undefined);
    // Update internal state if not controlled
    if (selectedPhase === undefined) {
      setInternalSelectedPhase(phase);
    }

    // Notify parent if callback provided
    if (onPhaseSelect) {
      onPhaseSelect(phase);
    }
  };

  const saveChanges = () => {
    console.log("Changes saved");
  };

  const changePhase = () => {
    // Update internal state if not controlled
    if (selectedPhase === undefined) {
      setInternalSelectedPhase(temporarySelectedPhase);
    }

    // Notify parent if callback provided
    if (onPhaseSelect && temporarySelectedPhase !== undefined) {
      onPhaseSelect(temporarySelectedPhase);
    }
  };

  const formatDate = (dateString?: string | null | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    return date.toLocaleDateString("es-ES", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getDateColor = (date?: string) => {
    if (!date) return "text-default-500";
    const currentDate = new Date();
    const dateObj = new Date(date);

    return dateObj < currentDate
      ? "font-bold text-red-500"
      : "text-default-500";
  };

  useEffect(() => {
    if (!projectData) return;

    // Find the current phase (first phase that is not completed)
    const currentPhase = projectData.phases.find((p) => !p.completed);

    const steps = [
      {
        label: "START",
        dates: [
          formatDate(
            projectData.dates.start_real_initial_date ||
              projectData.dates.start_initial_date,
          ),
          formatDate(
            projectData.dates.start_real_final_date ||
              projectData.dates.start_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "START")?.completed
          ? "completed"
          : currentPhase?.name === "START"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "START")?.percentage || 0,
        verified:
          projectData.phases.find((p) => p.name === "START")?.verified || false,
      },
      {
        label: "COLLECTION",
        dates: [
          formatDate(
            projectData.dates.collection_real_initial_date ||
              projectData.dates.collection_initial_date,
          ),
          formatDate(
            projectData.dates.collection_real_final_date ||
              projectData.dates.collection_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "COLLECTION")
          ?.completed
          ? "completed"
          : currentPhase?.name === "COLLECTION"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "COLLECTION")?.percentage ||
          0,
        verified:
          projectData.phases.find((p) => p.name === "COLLECTION")?.verified ||
          false,
      },
      {
        label: "MIGRATION",
        dates: [
          formatDate(
            projectData.dates.migration_real_initial_date ||
              projectData.dates.migration_initial_date,
          ),
          formatDate(
            projectData.dates.migration_real_final_date ||
              projectData.dates.migration_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "MIGRATION")
          ?.completed
          ? "completed"
          : currentPhase?.name === "MIGRATION"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "MIGRATION")?.percentage ||
          0,
        verified:
          projectData.phases.find((p) => p.name === "MIGRATION")?.verified ||
          false,
      },
      {
        label: "TEST",
        dates: [
          formatDate(
            projectData.dates.test_real_initial_date ||
              projectData.dates.test_initial_date,
          ),
          formatDate(
            projectData.dates.test_real_final_date ||
              projectData.dates.test_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "TEST")?.completed
          ? "completed"
          : currentPhase?.name === "TEST"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "TEST")?.percentage || 0,
        verified:
          projectData.phases.find((p) => p.name === "TEST")?.verified || false,
      },
      {
        label: "GO LIVE",
        dates: [
          formatDate(
            projectData.dates.golive_real_initial_date ||
              projectData.dates.golive_initial_date,
          ),
          formatDate(
            projectData.dates.golive_real_final_date ||
              projectData.dates.golive_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "GO LIVE")?.completed
          ? "completed"
          : currentPhase?.name === "GO LIVE"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "GO LIVE")?.percentage || 0,
        verified:
          projectData.phases.find((p) => p.name === "GO LIVE")?.verified ||
          false,
      },
      {
        label: "INCUBADORA",
        dates: [
          formatDate(
            projectData.dates.incubadora_real_initial_date ||
              projectData.dates.incubadora_initial_date,
          ),
          formatDate(
            projectData.dates.incubadora_real_final_date ||
              projectData.dates.incubadora_final_date,
          ),
        ],
        status: projectData.phases.find((p) => p.name === "INCUBADORA")
          ?.completed
          ? "completed"
          : currentPhase?.name === "INCUBADORA"
            ? "current"
            : "pending",
        progress:
          projectData.phases.find((p) => p.name === "INCUBADORA")?.percentage ||
          0,
        verified:
          projectData.phases.find((p) => p.name === "INCUBADORA")?.verified ||
          false,
      },
    ];

    setTimelineSteps(steps);
    console.log("Timeline steps:", steps);

    // Set initial selected phase to current phase if not already set
    if (!selectedPhase && !internalSelectedPhase && currentPhase) {
      setInternalSelectedPhase(currentPhase.name);
    }
  }, [projectData, selectedPhase, internalSelectedPhase]);

  return (
    <div className="relative pt-4">
      <div className="absolute top-[3.4rem] left-0 ml-10 h-1 bg-default-500 w-[50%]" />
      <div className="absolute top-[3.4rem] right-0 mr-11 h-1 bg-default-500 w-[50%]" />
      <div className="relative flex justify-between">
        {timelineSteps.map((step) => {
          // Get colors for this phase
          const colors = extractPhaseColors(step.label);
          const isSelected = effectiveSelectedPhase === step.label;

          return (
            <button
              key={step.label}
              aria-label={`Select ${step.label} phase`}
              className="flex flex-col items-center cursor-pointer bg-transparent border-none appearance-none"
              type="button"
              onClick={() => handlePhaseClick(step.label)}
            >
              <p
                className={`text-sm mb-2 ${isSelected ? "font-bold" : ""} transition-all duration-200 hover:text-primary`}
              >
                 {typeof step.label === "string"
                  ? step.label.toLowerCase() === "incubadora"
                    ? "TAKE OFF"
                    : step.label
                  : step.label} 
                {/*<LinkSplitEffect
                  fontSize="1"
                  text={
                    typeof step.label === "string"
                      ? step.label.toLowerCase() === "incubadora"
                        ? "TAKE OFF"
                        : step.label
                      : step.label
                  }
                />*/}

                {step.verified ? (
                  <svg
                    className="size-4 inline-block ml-1"
                    fill="none"
                    stroke="green"
                    strokeWidth={1.5}
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : (
                  ""
                )}
              </p>
              <div className="relative">
                <div
                  className={`w-6 h-6 rounded-full border-2 z-10 bg-background flex items-center justify-center overflow-hidden ${getPhaseStyle(step.label, false)} transition-all duration-200 ${step.progress > 0 && step.progress < 100 ? "" : "hover:scale-110"}`}
                >
                  {step.status !== "completed" && (
                    <span
                      className="hover:scale-110"
                      style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: "50%",
                        border: `2px solid ${colors.border}`,
                        background: `conic-gradient(${colors.border} 0%, ${colors.border} ${step.progress}%, ${colors.bg} ${step.progress}%, ${colors.bg} 100%)`,
                        position: "absolute",
                        transition: "transform 0.3s ease",
                      }}
                    />
                  )}

                  {step.status === "completed" && (
                    <span
                      className="hover:scale-110"
                      style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: "50%",
                        border: `2px solid ${colors.border}`,
                        background: `conic-gradient(${colors.bg} 0%, ${colors.bg} ${step.progress}%, ${colors.bg} ${step.progress}%, ${colors.bg} 100%)`,
                        position: "absolute",
                        transition: "transform 0.3s ease",
                      }}
                    >
                      ✓
                    </span>
                  )}
                </div>
              </div>
              <p className="text-xs text-default-500 mt-1">
                {Math.floor(step.progress)}%
              </p>
              <p className={`text-xs text-default-500 mt-1`}>
                <span>{step.dates[0]}</span>
                <br />{" "}
                {getDateColor(step.dates[1]) === "text-default-500" ? (
                  <span className={getDateColor(step.dates[1])}>
                    {step.dates[1]}
                  </span>
                ) : (
                  <span
                    className={getDateColor(step.dates[1])}
                    style={{
                      // For link--fire-gif-background
                      backgroundPositionY: "calc(1em + 16px)",
                      transition:
                        "background-position-y 0.6s ease-out, color 0.6s ease-out",
                      backgroundRepeat: "repeat-x",
                      backgroundImage: "url('/fire-gif-transparent.gif')",
                      backgroundSize: "contain",
                      padding: "4px",
                      // Hover effect will need to be handled with JavaScript/React state

                      // For link--fire-gif-pseudoelement
                      position: "relative",
                      display: "inline-block",
                      color: "red",
                      fontWeight: "bold",
                      // textShadow:
                      //   "0px 0px 4px white, 0px 0px 4px white, 0px 0px 4px white",
                      // transition: "color 0.6s ease",
                      // Pseudo-element will need to be handled differently
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundPositionY = "0";
                      e.currentTarget.style.color = "black";
                      // For pseudo-element effect, you'll need a different approach
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundPositionY =
                        "calc(1em + 16px)";
                      e.currentTarget.style.color = "red";
                      // Reset pseudo-element effect
                    }}
                  >
                    {step.dates[1]}
                  </span>
                )}
              </p>
            </button>
          );
        })}
      </div>

      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                ⚠️¡Cuidado!⚠️
              </ModalHeader>
              <ModalBody>
                <div className="flex flex-col gap-2">
                  <p className="text-sm text-default-500">
                    No has guardado los cambios. ¿Estás seguro de que deseas
                    continuar?
                  </p>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="default"
                  variant="light"
                  onPress={() => {
                    setIsModalOpen(false);
                    onClose();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  color="danger"
                  variant="solid"
                  onPress={() => {
                    changePhase();
                    setIsModalOpen(false);
                    onClose();
                  }}
                >
                  Continuar sin guardar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

const LinkSplitEffect = ({
  text,
  fontSize = "2em",
}: {
  text: string;
  fontSize?: string;
}) => {
  const styles = {
    container: {
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize,
    },
    link: {
      position: "relative" as const,
      display: "inline-block",
      padding: 0,
      lineHeight: "1em",
      margin: 0,
      textDecoration: "none",
    },
    linkTop: {
      position: "absolute" as const,
      color: "black",
      top: 0,
      display: "inline-block",
      clipPath: "polygon(0% 66%, 0% 0%, 100% 0%, 100% 40%)",
      transition:
        "transform 0.6s cubic-bezier(0.16, 1, 0.3, 1) 0.25s, color 0.5s ease 0.22s",
    },
    linkBottom: {
      display: "inline-block",
      clipPath: "polygon(0% 65%, 100% 40%, 100% 110%, 0% 110%)",
      color: "black",
      transition: "color 0.5s ease 0.22s, background-position 0.2s ease 0.22s",
      textDecoration: "none",
      backgroundSize: "200% 8%",
      backgroundPosition: "left bottom",
      backgroundRepeat: "no-repeat",
      // backgroundImage: "linear-gradient(to right, red 50%, transparent 50%)",
    },
  };

  return (
    <span style={styles.container}>
      <span className="split-link" rel="noreferrer" style={styles.link}>
        <span className="split-link-top" style={styles.linkTop}>
          {text}
        </span>
        <span className="split-link-bottom" style={styles.linkBottom}>
          {text}
        </span>
      </span>

      {/* Add the hover styles via a style tag */}
      {/* <style>{`
        .split-link-top::after {
          content: "";
          position: absolute;
          top: 36%;
          left: 0;
          width: 100%;
          height: 4%;
          background: red;
          transform: rotateZ(-2.2deg) scaleX(0%);
          transform-origin: right top;
          transition: transform 0.2s ease 0.22s;
        }

        .split-link-bottom::after {
          content: "";
          position: absolute;
          top: 40%;
          left: 0;
          width: 100%;
          height: 4%;
          background: red;
          transform: rotateZ(-2deg) scaleX(0%);
          transform-origin: right top;
          transition: transform 0.2s ease 0.22s;
        }

        .split-link:hover .split-link-top {
          color: red;
          transform: translateY(-0.5em) rotateZ(-3deg);
          transition:
            transform 0.5s cubic-bezier(0.12, 0.8, 0.57, 1) 0.42s,
            color 0.5s ease 0.22s;
        }

        .split-link:hover .split-link-bottom {
          color: red;
          background-position: 100% bottom;
          transition:
            color 0.5s ease 0.2s,
            background-position 0.2s ease;
        }

        .split-link:hover .split-link-top::after {
          top: 62%;
          transform-origin: left top;
          transform: rotateZ(-2.2deg) scaleX(100%);
        }

        .split-link:hover .split-link-bottom::after {
          top: 65%;
          transform-origin: left top;
          transform: rotateZ(-2.1deg) scaleX(100%);
        }
      `}</style> */}
    </span>
  );
};
