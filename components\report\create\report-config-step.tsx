"use client";

import React, { useEffect } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  Switch,
  Select,
  SelectItem,
} from "@heroui/react";
import { Icon } from "@iconify/react";

interface ReportConfig {
  name: string;
  name_en: string;
  description: string;
  description_en: string;
  documentName: string;
  documentName_en: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
  phases: boolean;
  subphases: boolean;
}

interface ReportConfigStepProps {
  config: ReportConfig;
  onConfigChange: (config: ReportConfig) => void;
  onValidationChange?: (isValid: boolean) => void;
}

export default function ReportConfigStep({
  config,
  onConfigChange,
  onValidationChange,
}: ReportConfigStepProps) {
  const handleInputChange = (
    field: keyof ReportConfig,
    value: string | boolean,
  ) => {
    onConfigChange({
      ...config,
      [field]: value,
    });
  };

  // Validation function to check if all required text fields are filled
  const isFormValid = () => {
    const requiredFields = [
      config.name,
      config.name_en,
      config.description,
      config.description_en,
      config.documentName,
      config.documentName_en,
    ];

    return requiredFields.every(field => field.trim().length > 0);
  };

  // Notify parent component about validation changes
  useEffect(() => {
    if (onValidationChange) {
      onValidationChange(isFormValid());
    }
  }, [config, onValidationChange]);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Configuración del reporte
        </h3>
        <p className="text-default-500">
          Define los datos básicos y configuraciones del reporte
        </p>
      </div>

      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:document-text"
              width={24}
            />
            <h4 className="text-lg font-semibold">Información básica</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0 space-y-4">
          <Input
            isRequired
            label="Nombre del reporte"
            placeholder="Ingresa el nombre del reporte"
            value={config.name}
            variant="bordered"
            maxLength={40}
            onValueChange={(value) => handleInputChange("name", value)}
          />

          <Input
            isRequired
            label="Nombre del reporte (English)"
            placeholder="Ingresa el nombre del reporte en inglés"
            value={config.name_en}
            variant="bordered"
            maxLength={40}
            onValueChange={(value) => handleInputChange("name_en", value)}
          />

          <Textarea
            isRequired
            label="Descripción"
            placeholder="Describe el propósito y contenido del reporte"
            value={config.description}
            variant="bordered"
            maxLength={140}
            onValueChange={(value) => handleInputChange("description", value)}
          />

          <Textarea
            isRequired
            label="Descripción (English)"
            placeholder="Describe el propósito y contenido del reporte en inglés"
            value={config.description_en}
            variant="bordered"
            maxLength={140}
            onValueChange={(value) => handleInputChange("description_en", value)}
          />

          <Input
            isRequired
            label="Nombre del documento"
            placeholder="Nombre del archivo que se generará"
            value={config.documentName}
            variant="bordered"
            maxLength={40}
            onValueChange={(value) => handleInputChange("documentName", value)}
          />

          <Input
            isRequired
            label="Nombre del documento (English)"
            placeholder="Nombre del archivo que se generará en inglés"
            value={config.documentName_en}
            variant="bordered"
            maxLength={40}
            onValueChange={(value) =>
              handleInputChange("documentName_en", value)
            }
          />
        </CardBody>
      </Card>

      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:cog-6-tooth"
              width={24}
            />
            <h4 className="text-lg font-semibold">Configuraciones</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0 space-y-4">
          {/* <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Icon
                className="text-default-500"
                icon="heroicons:language"
                width={20}
              />
              <div>
                <p className="font-medium">Idioma de los campos</p>
                <p className="text-sm text-default-500">
                  Seleccionar el idioma para los campos
                </p>
              </div>
            </div>
            <Select
              aria-label="Seleccionar idioma"
              className="w-40"
              disableAnimation={true}
              selectedKeys={[config.useEnglishFields ? "false" : "true"]}
              variant="bordered"
              onChange={(e) =>
                handleInputChange(
                  "useEnglishFields",
                  e.target.value === "false",
                )
              }
            >
              <SelectItem key="false">English</SelectItem>
              <SelectItem key="true">Español</SelectItem>
            </Select>
          </div> */}
          <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Icon
                className="text-default-500"
                icon="heroicons:inbox"
                width={20}
              />
              <div>
                <p className="font-medium">Generar las tablas por fases</p>
                <p className="text-sm text-default-500">
                  Separar las tablas por fases
                </p>
              </div>
            </div>
            <Switch
              checked={config.phases}
              onChange={(e) => handleInputChange("phases", e.target.checked)}
              className="h-6 w-11"
            >
              <span className="sr-only">Separar por fases</span>
            </Switch>
          </div>
          <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Icon
                className="text-default-500"
                icon="heroicons:inbox-stack"
                width={20}
              />
              <div>
                <p className="font-medium">Generar las tablas por subfases</p>
                <p className="text-sm text-default-500">
                  Separar las tablas por subfases
                </p>
              </div>
            </div>
            <Switch
              checked={config.subphases}
              onChange={(e) => handleInputChange("subphases", e.target.checked)}
              className="h-6 w-11"
            >
              <span className="sr-only">Separar por subfases</span>
            </Switch>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
