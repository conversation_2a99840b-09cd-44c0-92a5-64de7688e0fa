import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>ooter,
  ModalBody,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Progress,
} from "@heroui/react";

import { ApiProjectResponse, ApiPhase } from "@/types/project-api";
import { useVerifyProjectPhase } from "@/hooks/projects/useVerifyProjectPhase";
import { getPhaseIdByName } from "@/utils/project-transformers";

export interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectData?: ApiProjectResponse;
  onPhaseVerified?: () => void; // Callback to refresh project data
}

export const VerificationModal: React.FC<VerificationModalProps> = ({
  isOpen,
  onClose,
  projectData,
  onPhaseVerified,
}) => {
  const [phases, setPhases] = useState<ApiPhase[]>([]);
  const [verifyingPhaseId, setVerifyingPhaseId] = useState<number | null>(null);
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    phase: ApiPhase | null;
  }>({ isOpen: false, phase: null });

  const { verifyProjectPhase, loading, error } = useVerifyProjectPhase();

  // Update phases when projectData changes
  useEffect(() => {
    if (projectData?.phases) {
      // Sort phases by order to display them correctly
      const sortedPhases = [...projectData.phases].sort(
        (a, b) => a.order - b.order,
      );

      setPhases(sortedPhases);

      console.log("project data", projectData);
    }
  }, [projectData]);

  const showConfirmation = (phase: ApiPhase) => {
    setConfirmationModal({ isOpen: true, phase });
  };

  const hideConfirmation = () => {
    setConfirmationModal({ isOpen: false, phase: null });
  };

  const handleVerify = async (phase: ApiPhase) => {
    if (!projectData?.project?.id) return;

    try {
      setVerifyingPhaseId(phase.id);
      hideConfirmation();

      console.log("Verifying phase:", phase.name);
      const phaseId = getPhaseIdByName(projectData.phases, phase.name);

      if (phaseId !== null) {
        await verifyProjectPhase(projectData.project.id, phaseId);
      } else {
        console.error("Phase ID not found for:", phase.name);
      }

      // Update local state to reflect the verification change
      setPhases((prevPhases) =>
        prevPhases.map((p) =>
          p.id === phase.id ? { ...p, verified: !p.verified } : p,
        ),
      );

      // Call the callback to refresh project data if provided
      if (onPhaseVerified) {
        onPhaseVerified();
      }
    } catch (err) {
      console.error("Error verifying phase:", err);
    } finally {
      setVerifyingPhaseId(null);
    }
  };

  return (
    <>
      <Modal isOpen={isOpen} size="3xl" onClose={onClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Verificación de Fases
              </ModalHeader>
              <ModalBody>
                <Table removeWrapper aria-label="Tabla de verificación de fases">
                  <TableHeader>
                    <TableColumn>FASE</TableColumn>
                    <TableColumn>VERIFICADO</TableColumn>
                    <TableColumn>PROGRESO</TableColumn>
                    <TableColumn>ACCIÓN</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {phases.map((phase) => (
                      <TableRow key={phase.id}>
                        <TableCell>{phase.name}</TableCell>
                        <TableCell>
                          <span
                            className={`flex items-center gap-1 ${phase.verified ? "text-success" : "text-danger"}`}
                          >
                            <Icon
                              icon={phase.verified ? "lucide:check" : "lucide:x"}
                            />
                            {phase.verified ? "Verificado" : "No Verificado"}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress
                              className="max-w-md"
                              color={
                                phase.percentage === 100 ? "success" : "warning"
                              }
                              size="sm"
                              value={phase.percentage}
                            />
                              <span className="text-sm">{Math.floor(phase.percentage)}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            className="w-full"
                            color={phase.verified ? "default" : "primary"}
                            isDisabled={verifyingPhaseId !== null || phase.verified}
                            isLoading={verifyingPhaseId === phase.id}
                            size="sm"
                            onPress={() => showConfirmation(phase)}
                          >
                            {phase.verified ? "Verificada" : "Verificar"}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Confirmation Modal */}
      <Modal isOpen={confirmationModal.isOpen} size="sm" onClose={hideConfirmation}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <Icon icon="lucide:alert-triangle" className="text-warning text-2xl" />
                Confirmar Verificación
              </ModalHeader>
              <ModalBody>
                <p className="text-center">
                  ¿Estás seguro de que deseas verificar la fase{" "}
                  <strong>"{confirmationModal.phase?.name}"</strong>?
                </p>
                <p className="text-sm text-gray-500 text-center">
                  Esta acción no se puede deshacer.
                </p>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={hideConfirmation}>
                  Cancelar
                </Button>
                <Button
                  color="primary"
                  onPress={() => confirmationModal.phase && handleVerify(confirmationModal.phase)}
                >
                  Sí, Verificar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
