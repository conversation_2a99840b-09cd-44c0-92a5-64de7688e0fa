"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

interface ProductionTeam {
  id: number;
  name: string;
  code: string;
}

export function useProductionTeams() {
  const [productionTeams, setProductionTeams] = useState<ProductionTeam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProductionTeams = async () => {
    try {
      setLoading(true);
      setError(null);

      if (
        process.env.ENVIRONMENT === "Development" ||
        process.env.ENVIRONMENT === "Pre production"
      ) {
        const response = await axiosInstance.get(
          API_ROUTES.PRODUCTION_TEAMS_ROBUST,
        );

        if (response.status === 200) {
          setProductionTeams(response.data);
        } else {
          setError("Failed to fetch production teams");
        }
      } else {
        const response = await axiosInstance.get(API_ROUTES.PRODUCTION_TEAMS);

        if (response.status === 200) {
          setProductionTeams(response.data);
        } else {
          setError("Failed to fetch production teams");
        }
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch production teams");
    } finally {
      setLoading(false);
    }
  };

  return {
    productionTeams,
    loading,
    error,
    fetchProductionTeams,
  };
}
