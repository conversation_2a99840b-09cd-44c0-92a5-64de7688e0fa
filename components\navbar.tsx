"use client";

import {
  Navbar as Hero<PERSON><PERSON><PERSON><PERSON>,
  Navbar<PERSON><PERSON>nt,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import { Link } from "@heroui/link";
import NextLink from "next/link";
import Image from "next/image";
import { User } from "@heroui/user";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Skeleton,
} from "@heroui/react";
import React from "react";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";

import { UserSettings } from "./user-settings";

import { siteConfig } from "@/config/site";
import { ThemeSwitch } from "@/components/theme-switch";
import { useAuth } from "@/hooks/auth/useAuth";
import { useUserStore } from "@/store/use-user-store";
import { usePermissionGuard } from "@/hooks/usePermissionGuard";

export const Navbar = () => {
  const { theme } = useTheme();

  const { logout } = useAuth();
  const { userInfo } = useUserStore();
  const { checkAccess } = usePermissionGuard();

  const [isSettingsModalOpen, setIsSettingsModalOpen] = React.useState(false);
  const currentPath = usePathname();

  if (currentPath === "/login" || currentPath === "/login/reiniciar") {
    return null;
  }

  // Filter navigation items based on user permissions
  const filteredNavItems = siteConfig.navItems.filter(
    (item) => !item.permission || checkAccess({ permission: item.permission }),
  );

  const filteredNavMenuItems = siteConfig.navMenuItems.filter(
    (item) => !item.permission || checkAccess({ permission: item.permission }),
  );

  const UserAvatar = (name: string) => {
    if (!name || userInfo === null) {
      return (
        <div className="w-[160px] flex items-center gap-3">
          <div>
            <Skeleton className="flex rounded-full w-8 h-8" />
          </div>
          <div className="w-full flex flex-col gap-2">
            <Skeleton className="h-3 w-4/5 rounded-lg" />
            <Skeleton className="h-3 w-full rounded-lg" />
          </div>
        </div>
      );
    }

    return (
      <User
        key={theme}
        avatarProps={{
          className: "h-8 w-8",
          // alt: userInfo?.name,
          // name: userInfo?.name,
          // src: "/broggo_pfp.png",
          classNames: {
            icon: theme === "dark" ? "text-white" : "text-black/80",
          },
          style: {
            background:
              theme === "dark"
                ? `linear-gradient(300deg, deepskyblue,darkviolet,blue)`
                : `linear-gradient(300deg, rgb(246, 211, 101), rgb(253, 160, 133), rgb(255, 195, 160), rgb(255, 175, 189))`,
            backgroundSize: "240% 240%",
            animation: "gradient-animation 10s ease infinite",
          },
        }}
        description={userInfo?.email}
        name={userInfo?.name}
      />
    );
  };

  return (
    <HeroUINavbar maxWidth="full" position="sticky">
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex justify-start items-center gap-1" href="/">
            <Image
              key={theme}
              alt={"Rosclar Logo"}
              height={100}
              src={theme === "dark" ? "/logo_dark.png" : "/logo.png"}
              width={100}
            />
          </NextLink>
        </NavbarBrand>
        <ul className="hidden md:flex gap-4 justify-start ml-2">
          {filteredNavItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                className={currentPath === item.href ? "font-semibold" : ""}
                color={currentPath === item.href ? "primary" : "foreground"}
                href={item.href}
              >
                {item.label}
              </NextLink>
            </NavbarItem>
          ))}
        </ul>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <NavbarItem className="hidden lg:flex cursor-pointer">
          <ThemeSwitch />
        </NavbarItem>
        <NavbarItem className="hidden lg:flex cursor-pointer">
          <Dropdown>
            <DropdownTrigger>{UserAvatar("Jane Doe")}</DropdownTrigger>
            <DropdownMenu aria-label="Static Actions">
              <>
                <DropdownItem
                  key="profile"
                  onPress={() => setIsSettingsModalOpen(true)}
                >
                  Ajustes de usuario
                </DropdownItem>
                <DropdownItem
                  key="logout"
                  className="text-danger"
                  color="danger"
                  onPress={logout}
                >
                  Cerrar sesión
                </DropdownItem>
              </>
            </DropdownMenu>
          </Dropdown>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <ThemeSwitch />
        <NavbarMenuToggle />
      </NavbarContent>

      <NavbarMenu>
        <div className="mx-4 mt-2 flex flex-col gap-2">
          {filteredNavMenuItems.map((item, index) => (
            <NavbarMenuItem key={`${item}-${index}`}>
              <Link
                color={
                  index === filteredNavMenuItems.length - 1
                    ? "danger"
                    : "foreground"
                }
                href={item.href}
                size="lg"
              >
                {item.label}
              </Link>
            </NavbarMenuItem>
          ))}
        </div>
      </NavbarMenu>

      <UserSettings
        isOpen={isSettingsModalOpen}
        user={userInfo}
        onClose={() => setIsSettingsModalOpen(false)}
      />
    </HeroUINavbar>
  );
};
